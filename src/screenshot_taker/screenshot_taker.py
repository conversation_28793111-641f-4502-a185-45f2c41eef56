"""


"""

import os
from datetime import datetime
from pathlib import Path
from typing import List

import mss
from mss import tools
from loguru import logger as log


def take_all_monitors_screenshot(output_dir: str = "/output-temp") -> List[Path]:
    """
    Take screenshots of all available monitors and save to specified directory.
    
    Args:
        output_dir: Directory to save screenshots (default: /output-temp, falls back to ./output-temp)
        
    Returns:
        List of Path objects for saved screenshot files
        
    Raises:
        OSError: If unable to create output directory or save files
        RuntimeError: If no monitors detected or screenshot fails
    """
    # Try to create output directory, fallback to relative path if root is read-only
    output_path = Path(output_dir)
    try:
        output_path.mkdir(parents=True, exist_ok=True)
        log.info(f"Screenshot output directory: {output_path.absolute()}")
    except OSError as e:
        if output_dir.startswith('/') and 'Read-only file system' in str(e):
            # Fallback to relative path in project directory
            fallback_dir = "./output-temp"
            log.warning(f"Cannot write to {output_dir} (read-only), using fallback: {fallback_dir}")
            output_path = Path(fallback_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            log.info(f"Screenshot output directory: {output_path.absolute()}")
        else:
            raise
    
    saved_files: List[Path] = []
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    with mss.mss() as sct:
        # Get all monitors
        monitors = sct.monitors
        log.info(f"Detected {len(monitors)} monitors (including virtual)")
        
        if len(monitors) <= 1:  # mss includes a virtual "all monitors" at index 0
            raise RuntimeError("No physical monitors detected")
        
        # Screenshot each monitor (skip index 0 which is virtual "all monitors")
        for i, monitor in enumerate(monitors[1:], 1):
            filename = f"monitor_{i}_{timestamp}.png"
            filepath = output_path / filename
            
            try:
                # Take screenshot of this monitor
                screenshot = sct.grab(monitor)
                # Save as PNG
                tools.to_png(screenshot.rgb, screenshot.size, output=str(filepath))
                
                saved_files.append(filepath)
                log.info(f"Monitor {i} screenshot saved: {filepath}")
                
            except Exception as e:
                log.error(f"Failed to capture monitor {i}: {e}")
                continue
    
    if not saved_files:
        raise RuntimeError("Failed to capture any monitor screenshots")
    
    log.info(f"Successfully captured {len(saved_files)} monitor screenshots")
    return saved_files


# Convenience function for direct execution
def main() -> None:
    """Main function for direct script execution."""
    try:
        files = take_all_monitors_screenshot()
        print(f"Screenshots saved to: {[str(f) for f in files]}")
    except Exception as e:
        log.error(f"Screenshot capture failed: {e}")
        raise


if __name__ == "__main__":
    main()